import React, { createContext, useContext, useEffect, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { apiService } from '@services/apiService';
import { firebaseService } from '@services/firebaseService';

const AuthContext = createContext({});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isFirstLaunch, setIsFirstLaunch] = useState(null);

  useEffect(() => {
    checkFirstLaunch();
    checkAuthState();
  }, []);

  const checkFirstLaunch = async () => {
    try {
      const hasLaunched = await AsyncStorage.getItem('hasLaunched');
      if (hasLaunched === null) {
        setIsFirstLaunch(true);
        await AsyncStorage.setItem('hasLaunched', 'true');
      } else {
        setIsFirstLaunch(false);
      }
    } catch (error) {
      console.error('Error checking first launch:', error);
      setIsFirstLaunch(false);
    }
  };

  const checkAuthState = async () => {
    try {
      setLoading(true);
      
      // Check for stored auth token
      const token = await SecureStore.getItemAsync('authToken');
      const userData = await AsyncStorage.getItem('userData');
      
      if (token && userData) {
        const parsedUserData = JSON.parse(userData);
        setUser(parsedUserData);
        
        // Verify token with backend and get fresh user data
        try {
          const response = await apiService.get('/auth/profile');
          if (response.success) {
            setUserProfile(response.data);
          }
        } catch (error) {
          console.error('Token verification failed:', error);
          await logout();
        }
      }
    } catch (error) {
      console.error('Error checking auth state:', error);
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      setLoading(true);
      
      // Sign in with Firebase
      const firebaseResult = await firebaseService.auth.signInWithEmailAndPassword(email, password);
      
      if (firebaseResult.success && firebaseResult.user) {
        // Store user data
        setUser(firebaseResult.user);
        setIsAuthenticated(true);

        // Store a mock token for API calls
        await SecureStore.setItemAsync('authToken', 'mock-auth-token-' + firebaseResult.user.uid);
        await AsyncStorage.setItem('userData', JSON.stringify(firebaseResult.user));

        return { success: true };
      } else {
        throw new Error('Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (userData) => {
    try {
      setLoading(true);
      
      // Create Firebase user
      const firebaseUser = await firebaseService.createUserWithEmail(
        userData.email,
        userData.password
      );
      
      if (firebaseUser) {
        // Update Firebase profile
        await firebaseService.updateProfile(firebaseUser, {
          displayName: `${userData.firstName} ${userData.lastName}`,
        });
        
        // Get ID token
        const idToken = await firebaseUser.getIdToken();
        
        // Register with backend as Work-Boy
        const response = await apiService.post('/auth/register', {
          firebase_uid: firebaseUser.uid,
          email: userData.email,
          first_name: userData.firstName,
          last_name: userData.lastName,
          phone: userData.phone,
          user_type: 'workboy',
          id_token: idToken,
          // Work-Boy specific fields
          skills: userData.skills || [],
          experience_years: userData.experienceYears || 0,
          service_radius: userData.serviceRadius || 10,
        });
        
        if (response.success) {
          // Store auth data
          await SecureStore.setItemAsync('authToken', response.data.token);
          await AsyncStorage.setItem('userData', JSON.stringify(firebaseUser));
          
          setUser(firebaseUser);
          setUserProfile(response.data.user);
          
          return { success: true };
        } else {
          // If backend registration fails, delete Firebase user
          await firebaseUser.delete();
          throw new Error(response.message || 'Registration failed');
        }
      }
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      
      // Sign out from Firebase
      await firebaseService.signOut();
      
      // Clear stored data
      await SecureStore.deleteItemAsync('authToken');
      await AsyncStorage.removeItem('userData');
      
      setUser(null);
      setUserProfile(null);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (profileData) => {
    try {
      setLoading(true);
      
      const response = await apiService.put('/auth/profile', profileData);
      
      if (response.success) {
        setUserProfile(response.data);
        
        // Update Firebase profile if name changed
        if (profileData.first_name || profileData.last_name) {
          await firebaseService.updateProfile(user, {
            displayName: `${profileData.first_name || userProfile.first_name} ${profileData.last_name || userProfile.last_name}`,
          });
        }
        
        return { success: true };
      } else {
        throw new Error(response.message || 'Profile update failed');
      }
    } catch (error) {
      console.error('Profile update error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateAvailability = async (isAvailable) => {
    try {
      const response = await apiService.put('/workboy/availability', {
        is_available: isAvailable,
      });
      
      if (response.success) {
        setUserProfile(prev => ({
          ...prev,
          is_available: isAvailable,
        }));
        
        return { success: true };
      } else {
        throw new Error(response.message || 'Failed to update availability');
      }
    } catch (error) {
      console.error('Availability update error:', error);
      throw error;
    }
  };

  const submitKYC = async (kycData) => {
    try {
      setLoading(true);
      
      const response = await apiService.post('/workboy/kyc', kycData);
      
      if (response.success) {
        setUserProfile(prev => ({
          ...prev,
          kyc_status: 'pending',
          kyc_submitted_at: new Date().toISOString(),
        }));
        
        return { success: true };
      } else {
        throw new Error(response.message || 'KYC submission failed');
      }
    } catch (error) {
      console.error('KYC submission error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email) => {
    try {
      await firebaseService.sendPasswordResetEmail(email);
      return { success: true };
    } catch (error) {
      console.error('Password reset error:', error);
      throw error;
    }
  };

  const value = {
    // State
    user,
    userProfile,
    loading,
    isFirstLaunch,
    
    // Methods
    login,
    register,
    logout,
    updateProfile,
    updateAvailability,
    submitKYC,
    resetPassword,
    
    // Computed values
    isAuthenticated: !!user,
    isWorkBoy: userProfile?.user_type === 'workboy',
    isAvailable: userProfile?.is_available || false,
    kycStatus: userProfile?.kyc_status || 'not_submitted',
    isKYCApproved: userProfile?.kyc_status === 'approved',
    canWork: userProfile?.kyc_status === 'approved' && userProfile?.is_available,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
